import { useState } from 'react';
import { message } from 'antd';
import type { FormInstance } from 'antd';

// 导入重构后的模块
import type { TaskBasic, TaskAlert, DBConnection, AlertSend, OtherInfo } from '../types';
import { TaskService } from '../services';
import { formatFrequencyToString } from '../../../utils/frequencyConverter';

interface UseFormSubmitProps {
  form: FormInstance;
  isEditMode: boolean;
  initialData?: TaskBasic;
  alerts: TaskAlert[];
  alertSends: AlertSend[];
  dbConnection: DBConnection | null;
  otherInfo: OtherInfo | null;
  onSubmit?: () => void;
}

interface UseFormSubmitReturn {
  submitLoading: boolean;
  validateForm: () => Promise<boolean>;
  getFormData: () => any;
  handleFormSubmit: () => Promise<void>;
}

/**
 * 表单提交逻辑 Hook
 * 处理表单验证和提交逻辑
 */
export const useFormSubmit = ({
  form,
  isEditMode,
  initialData,
  alerts,
  alertSends,
  dbConnection,
  otherInfo,
  onSubmit,
}: UseFormSubmitProps): UseFormSubmitReturn => {
  const [submitLoading, setSubmitLoading] = useState(false);

  // 表单验证
  const validateForm = async (): Promise<boolean> => {
    try {
      await form.validateFields();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  // 获取表单数据
  const getFormData = () => {
    return form.getFieldsValue();
  };

  // 表单提交处理
  const handleFormSubmit = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      message.error('请检查表单数据');
      return;
    }

    setSubmitLoading(true);
    try {
      const formData = getFormData();

      // 转换表单数据为提交格式
      const submitData = {
        ...formData,
        weekday: Array.isArray(formData.weekday) ? formData.weekday.join(',') : formData.weekday,
        frequency: formatFrequencyToString(formData.frequency_value, formData.frequency_unit),
        retry_frequency: formatFrequencyToString(formData.retry_frequency_value, formData.retry_frequency_unit),
        start_time: formData.start_time ? formData.start_time.format('HH:mm:ss') : '',
        end_time: formData.end_time ? formData.end_time.format('HH:mm:ss') : '',
        alert_task_id: alerts.map(alert => `alert_${alert.id}`).join(','),
        alert_send_id: alertSends.map(send => `send_${send.id}`).join(','),
        db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
        other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
      };

      // 调用API
      if (isEditMode && initialData) {
        await TaskService.updateComplexForm(initialData.id, { id: initialData.id, ...submitData });
        message.success('更新任务成功');
      } else {
        await TaskService.saveComplexForm(submitData);
        message.success('创建任务成功');
      }

      onSubmit?.();
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error(isEditMode ? '更新任务失败' : '创建任务失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  return {
    submitLoading,
    validateForm,
    getFormData,
    handleFormSubmit,
  };
};
