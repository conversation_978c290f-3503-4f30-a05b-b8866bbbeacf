import React from 'react';
import { Form, Input, Select, Card, Row, Col, InputNumber, TimePicker, Space } from 'antd';
import type { FormInstance } from 'antd';

// 导入重构后的模块
import { WEEKDAY_OPTIONS, FREQUENCY_UNIT_OPTIONS, RETRY_FREQUENCY_UNIT_OPTIONS, DEFAULT_RETRY_NUM, DEFAULT_RETRY_FREQUENCY, FORM_PLACEHOLDERS, TASK_STATUS_OPTIONS } from '../constants';
import { TaskGroupSelect } from './common/TaskGroupSelect';
import { formStyles } from '../styles';

const { Option } = Select;

interface BasicInfoFormProps {
  form: FormInstance;
}

/**
 * 基础信息表单组件
 * 包含任务的基本配置信息
 */
const BasicInfoForm: React.FC<BasicInfoFormProps> = ({ form }) => {
  return (
    <div className={formStyles.tabContent}>
      <Card title='基本配置' size='small' className='mb-4'>
        <Row gutter={16} className='mb-4'>
          <Col span={8}>
            <Form.Item label='任务名称' name='name' rules={[{ required: true, message: '请输入任务名称' }]}>
              <Input placeholder={FORM_PLACEHOLDERS.name} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='任务分组' name='group_name' rules={[{ required: true, message: '请选择任务分组' }]}>
              <TaskGroupSelect placeholder={FORM_PLACEHOLDERS.group} allowClear dynamicSearch={true} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16} className='mb-4'>
          <Col span={8}>
            <Form.Item label='开始时间' name='start_time' rules={[{ required: true, message: '请选择开始时间' }]}>
              <TimePicker placeholder={FORM_PLACEHOLDERS.startTime} format='HH:mm:ss' className='w-full' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='结束时间' name='end_time' rules={[{ required: true, message: '请选择结束时间' }]}>
              <TimePicker placeholder={FORM_PLACEHOLDERS.endTime} format='HH:mm:ss' className='w-full' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='任务状态' name='status' rules={[{ required: true, message: '请选择任务状态' }]}>
              <Select placeholder='请选择任务状态'>
                {TASK_STATUS_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16} className='mb-4'>
          <Col span={12}>
            <Form.Item label='执行星期' name='weekday' rules={[{ required: true, message: '请选择执行星期' }]}>
              <Select mode='multiple' placeholder={FORM_PLACEHOLDERS.weekday} allowClear>
                {WEEKDAY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='执行频率'>
              <Space.Compact style={{ width: '100%' }}>
                <Form.Item name='frequency_value' noStyle rules={[{ required: true, message: '请输入频率值' }]}>
                  <InputNumber placeholder='频率值' min={1} style={{ width: '80%' }} />
                </Form.Item>
                <Form.Item name='frequency_unit' noStyle rules={[{ required: true, message: '请选择频率单位' }]}>
                  <Select placeholder='单位' style={{ width: '20%' }}>
                    {FREQUENCY_UNIT_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Space.Compact>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16} className='mb-4'>
          <Col span={12}>
            <Form.Item label='重试次数' name='retry_num' initialValue={DEFAULT_RETRY_NUM}>
              <InputNumber placeholder={FORM_PLACEHOLDERS.retryNum} min={0} className='!w-full input-number-full' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='重试间隔'>
              <Space.Compact style={{ width: '100%' }}>
                <Form.Item name='retry_frequency_value' noStyle initialValue={DEFAULT_RETRY_FREQUENCY.value}>
                  <InputNumber placeholder='间隔值' min={1} style={{ width: '80%' }} />
                </Form.Item>
                <Form.Item name='retry_frequency_unit' noStyle initialValue={DEFAULT_RETRY_FREQUENCY.unit}>
                  <Select placeholder='单位' style={{ width: '20%' }}>
                    {RETRY_FREQUENCY_UNIT_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Space.Compact>
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default BasicInfoForm;
