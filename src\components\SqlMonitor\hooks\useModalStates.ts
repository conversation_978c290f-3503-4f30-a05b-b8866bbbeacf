import { useState } from 'react';

interface ModalState {
  visible: boolean;
  editingIndex: number;
}

interface SelectModalState {
  visible: boolean;
  type: 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo';
  multiple: boolean;
}

interface UseModalStatesReturn {
  alertModal: ModalState;
  dbConnectionModal: ModalState;
  alertSendModal: ModalState;
  otherInfoModal: ModalState;
  selectModal: SelectModalState;
  setAlertModal: (state: ModalState) => void;
  setDbConnectionModal: (state: ModalState) => void;
  setAlertSendModal: (state: ModalState) => void;
  setOtherInfoModal: (state: ModalState) => void;
  setSelectModal: (state: SelectModalState) => void;
  closeAllModals: () => void;
}

/**
 * 模态框状态管理 Hook
 * 统一管理所有模态框的显示状态
 */
export const useModalStates = (): UseModalStatesReturn => {
  // Modal状态
  const [alertModal, setAlertModal] = useState<ModalState>({
    visible: false,
    editingIndex: -1,
  });

  const [dbConnectionModal, setDbConnectionModal] = useState<ModalState>({
    visible: false,
  });

  const [alertSendModal, setAlertSendModal] = useState<ModalState>({
    visible: false,
    editingIndex: -1,
  });

  const [otherInfoModal, setOtherInfoModal] = useState<ModalState>({
    visible: false,
  });

  const [selectModal, setSelectModal] = useState<SelectModalState>({
    visible: false,
    type: 'alert',
    multiple: false,
  });

  // 关闭所有模态框
  const closeAllModals = () => {
    setAlertModal({ visible: false, editingIndex: -1 });
    setDbConnectionModal({ visible: false });
    setAlertSendModal({ visible: false, editingIndex: -1 });
    setOtherInfoModal({ visible: false });
    setSelectModal({ visible: false, type: 'alert', multiple: false });
  };

  return {
    alertModal,
    dbConnectionModal,
    alertSendModal,
    otherInfoModal,
    selectModal,
    setAlertModal,
    setDbConnectionModal,
    setAlertSendModal,
    setOtherInfoModal,
    setSelectModal,
    closeAllModals,
  };
};
